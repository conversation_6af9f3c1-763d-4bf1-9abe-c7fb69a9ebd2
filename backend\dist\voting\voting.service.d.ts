import { PrismaService } from "src/prisma/prisma.service";
import { Common } from "@commune/api";
type CreateDto = {
    votesRequired: number;
    endsAt: Date;
    title: Common.Localization[];
    description: Common.Localization[];
    options: {
        title: Common.Localization[];
    }[];
};
export declare class VotingService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(data: CreateDto): Promise<{
        description: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        options: ({
            title: {
                value: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            votingId: string;
        })[];
        title: {
            value: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }>;
}
export {};
