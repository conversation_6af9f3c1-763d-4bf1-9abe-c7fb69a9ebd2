<script lang="ts">
  import type { Common } from "@commune/api";

  import { onMount } from "svelte";
  import { formatDate } from "$lib";
  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import { goto, replaceState } from "$app/navigation";
  import {
    Modal,
    LocalizedInput,
    LocalizedTextarea,
    UserPicker,
    ReactorHubPicker,
  } from "$lib/components";

  const i18n = {
    en: {
      _page: { title: "Communities — Reactor of Commune" },
      communities: "Communities",
      createCommunity: "Create Community",
      noCommunities: "No communities found",
      head: "Head",
      errorFetchingCommunities: "Failed to fetch communities",
      errorOccurred: "An error occurred while fetching communities",
      loadingMore: "Loading more communities...",
      createdOn: "Created on",
      createCommunityTitle: "Create New Community",
      communityName: "Community Name",
      communityDescription: "Community Description",
      headUser: "Head User",
      headUserPlaceholder: "Leave empty to use current user",
      hub: "Hub",
      hubPlaceholder: "Leave empty for no hub",
      communityNamePlaceholder: "Enter community name",
      communityDescriptionPlaceholder: "Enter community description",
      create: "Create",
      cancel: "Cancel",
      creating: "Creating...",
      communityCreatedSuccess: "Community created successfully!",
      errorCreatingCommunity: "Failed to create community",
      required: "This field is required",
      searchPlaceholder: "Search communities...",
      noHub: "No hub",
    },
    ru: {
      _page: { title: "Сообщества — Реактор Коммуны" },
      communities: "Сообщества",
      createCommunity: "Создать сообщество",
      noCommunities: "Сообщества не найдены",
      head: "Глава",
      errorFetchingCommunities: "Не удалось загрузить сообщества",
      errorOccurred: "Произошла ошибка при загрузке сообществ",
      loadingMore: "Загружаем больше сообществ...",
      createdOn: "Создано",
      createCommunityTitle: "Создать новое сообщество",
      communityName: "Название сообщества",
      communityDescription: "Описание сообщества",
      headUser: "Глава",
      headUserPlaceholder: "Оставьте пустым для использования текущего пользователя",
      hub: "Хаб",
      hubPlaceholder: "Оставьте пустым для отсутствия хаба",
      communityNamePlaceholder: "Введите название сообщества",
      communityDescriptionPlaceholder: "Введите описание сообщества",
      create: "Создать",
      cancel: "Отмена",
      creating: "Создаем...",
      communityCreatedSuccess: "Сообщество успешно создано!",
      errorCreatingCommunity: "Не удалось создать сообщество",
      required: "Это поле обязательно",
      searchPlaceholder: "Поиск сообществ...",
      noHub: "Нет хаба",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);
  const t = $derived(i18n[locale]);

  // State management
  let communities = $state(data.communities);
  let error = $state<string | null>(null);
  let showCreateModal = $state(false);
  let searchInputValue = $state(data.searchQuery || "");
  let searchDebounceTimeout = $state<ReturnType<typeof setTimeout> | null>(null);
  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreCommunities = $state(data.isHasMoreCommunities);
  let sentinelElement = $state<HTMLElement | null>(null);

  // Create community modal state
  let isCreating = $state(false);
  let createError = $state<string | null>(null);
  let createSuccess = $state<string | null>(null);
  let communityName = $state<Common.Localizations>([]);
  let communityDescription = $state<Common.Localizations>([]);
  let headUserId = $state<string | null>(null);
  let hubId = $state<string | null>(null);

  async function searchCommunities(query: string, page: number = 1, append: boolean = false) {
    if (!append) isLoadingMore = true;
    error = null;

    try {
      const newCommunities = await api.reactor.community.list.get({
        pagination: { page },
        query: query.trim() || undefined,
      });

      if (append) {
        communities = [...communities, ...newCommunities];
        currentPage = page;
      } else {
        communities = newCommunities;
        currentPage = 1;
      }

      isHasMoreCommunities = newCommunities.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  async function loadMoreCommunities() {
    if (isLoadingMore || !isHasMoreCommunities) return;
    await searchCommunities(searchInputValue, currentPage + 1, true);
  }

  // Event handlers
  function handleSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newQuery = target.value;

    updateSearchUrl(newQuery);
    debounceSearch(newQuery);
  }

  function updateSearchUrl(query: string) {
    const url = new URL(window.location.href);
    if (query.trim()) {
      url.searchParams.set("search", encodeURIComponent(query));
    } else {
      url.searchParams.delete("search");
    }
    replaceState(url.pathname + url.search, {});
  }

  function debounceSearch(query: string) {
    if (searchDebounceTimeout) {
      clearTimeout(searchDebounceTimeout);
    }

    searchDebounceTimeout = setTimeout(() => {
      searchCommunities(query);
    }, 1000);
  }

  // Modal handlers
  function openCreateModal() {
    showCreateModal = true;
    resetCreateForm();
  }

  function closeCreateModal() {
    showCreateModal = false;
  }

  function resetCreateForm() {
    communityName = [];
    communityDescription = [];
    headUserId = null;
    hubId = null;
    createError = null;
    createSuccess = null;
    isCreating = false;
  }

  function validateCreateForm(): boolean {
    if (!communityName.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }

    if (!communityDescription.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }

    return true;
  }

  async function handleCreateCommunity() {
    if (!validateCreateForm()) return;

    isCreating = true;
    createError = null;
    createSuccess = null;

    try {
      const { id } = await api.reactor.community.post({
        hubId: hubId || null,
        headUserId: headUserId,
        name: communityName,
        description: communityDescription,
      });

      createSuccess = t.communityCreatedSuccess;

      setTimeout(() => {
        goto(toLocaleHref(`/reactor/communities/${id}`));
      }, 1500);
    } catch (err) {
      createError = err instanceof Error ? err.message : t.errorCreatingCommunity;
      console.error(err);
    } finally {
      isCreating = false;
    }
  }

  function truncateDescription(text: string, maxLength: number = 200): string {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + "...";
  }

  function setupIntersectionObserver() {
    if (!sentinelElement) return null;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && isHasMoreCommunities && !isLoadingMore) {
          loadMoreCommunities();
        }
      },
      {
        rootMargin: "100px",
        threshold: 0.1,
      },
    );

    observer.observe(sentinelElement);
    return observer;
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver | null = null;

    const initObserver = () => {
      observer = setupIntersectionObserver();
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      initObserver();
    } else {
      setTimeout(initObserver, 100);
    }

    // Cleanup on component destroy
    return () => {
      observer?.disconnect();
      if (searchDebounceTimeout) {
        clearTimeout(searchDebounceTimeout);
      }
    };
  });
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4">
    <h1 class="mb-0">{t.communities}</h1>

    {#if data.user?.role === "admin"}
      <button class="btn btn-primary" onclick={openCreateModal}>
        <i class="bi bi-plus-circle me-2"></i>
        {t.createCommunity}
      </button>
    {/if}
  </div>

  <!-- Search Input -->
  <div class="mb-4">
    <div class="search-container">
      <input
        type="text"
        class="form-control"
        placeholder={t.searchPlaceholder}
        bind:value={searchInputValue}
        oninput={handleSearchChange}
        style="max-width: 400px;"
      />
    </div>
  </div>

  {#if communities.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noCommunities}</p>
    </div>
  {:else}
    <div class="row g-4">
      {#each communities as community (community.id)}
        <div class="col-12">
          <div class="card shadow-sm h-100">
            <div class="row g-0 h-100">
              <!-- Community Image -->
              <div class="col-md-3 col-lg-2">
                <div class="community-image-container">
                  {#if community.image}
                    <img
                      src={`/images/${community.image}`}
                      alt={getAppropriateLocalization(community.name) || "Community"}
                      class="community-image"
                    />
                  {:else}
                    <div class="community-image-placeholder">
                      <i class="bi bi-people fs-1 text-muted"></i>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Community Content -->
              <div class="col-md-9 col-lg-10">
                <div class="card-body d-flex flex-column h-100 p-4">
                  <!-- Community Name and Creation Date -->
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h4 class="card-title mb-0 flex-grow-1">
                      <a
                        href={toLocaleHref(`/reactor/communities/${community.id}`)}
                        style="text-decoration: none;"
                      >
                        {getAppropriateLocalization(community.name) || "No name?"}
                      </a>
                    </h4>
                    <!-- <small class="text-muted ms-3">
                      {t.createdOn}
                      {formatDate(community.createdAt, locale)}
                    </small> -->
                  </div>

                  <!-- Community Description -->
                  <p class="card-text text-muted mb-3 flex-grow-1">
                    {truncateDescription(getAppropriateLocalization(community.description) || "")}
                  </p>

                  <!-- Hub Info (if exists) -->
                  {#if community.hub}
                    <div class="mb-3">
                      <div class="d-flex align-items-center">
                        <div class="me-3">
                          {#if community.hub.image}
                            <img
                              src={`/images/${community.hub.image}`}
                              alt={getAppropriateLocalization(community.hub.name)}
                              class="rounded"
                              style="width: 32px; height: 32px; object-fit: cover;"
                            />
                          {:else}
                            <div
                              class="rounded bg-secondary d-flex align-items-center justify-content-center"
                              style="width: 32px; height: 32px;"
                            >
                              <i class="bi bi-collection text-white"></i>
                            </div>
                          {/if}
                        </div>
                        <div>
                          <!-- <div class="small text-muted">{t.hub}:</div> -->
                          <a
                            href={toLocaleHref(`/reactor/hubs/${community.hub.id}`)}
                            class="fw-medium"
                            style="text-decoration: none;"
                          >
                            {getAppropriateLocalization(community.hub.name)}
                          </a>
                        </div>
                      </div>
                    </div>
                  {/if}

                  <!-- Head User Info -->
                  <div class="d-flex align-items-center">
                    <div class="me-3">
                      {#if community.headUser.image}
                        <img
                          src={`/images/${community.headUser.image}`}
                          alt={getAppropriateLocalization(community.headUser.name)}
                          class="rounded"
                          style="width: 48px; height: 48px; object-fit: cover;"
                        />
                      {:else}
                        <div
                          class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                          style="width: 48px; height: 48px;"
                        >
                          <i class="bi bi-person-fill text-white"></i>
                        </div>
                      {/if}
                    </div>
                    <div>
                      <!-- <div class="small text-muted">{t.head}:</div> -->
                      <a
                        href={toLocaleHref(`/people/${community.headUser.id}`)}
                        class="fw-medium"
                        style="text-decoration: none;"
                      >
                        {getAppropriateLocalization(community.headUser.name)}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreCommunities}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>

<!-- Create Community Modal -->
{#if data.user?.role === "admin"}
  <Modal
    show={showCreateModal}
    title={t.createCommunityTitle}
    onClose={closeCreateModal}
    onSubmit={handleCreateCommunity}
    submitText={isCreating ? t.creating : t.create}
    cancelText={t.cancel}
    submitDisabled={isCreating ||
      !communityName.some((item) => item.value.trim().length > 0) ||
      !communityDescription.some((item) => item.value.trim().length > 0)}
    isSubmitting={isCreating}
  >
    {#if createError}
      <div class="alert alert-danger mb-3">
        {createError}
      </div>
    {/if}

    {#if createSuccess}
      <div class="alert alert-success mb-3">
        {createSuccess}
      </div>
    {/if}

    <form>
      <!-- Head User Picker -->
      <UserPicker
        bind:selectedUserId={headUserId}
        {locale}
        label={t.headUser}
        placeholder={t.headUserPlaceholder}
      />
      <div class="form-text mb-3">
        {t.headUserPlaceholder}
      </div>

      <!-- Hub Picker -->
      <ReactorHubPicker
        bind:selectedHubId={hubId}
        {locale}
        label={t.hub}
        placeholder={t.hubPlaceholder}
      />
      <div class="form-text mb-3">
        {t.hubPlaceholder}
      </div>

      <!-- Community Name Input -->
      <LocalizedInput
        {locale}
        id="community-name"
        label={t.communityName}
        placeholder={t.communityNamePlaceholder}
        required
        bind:value={communityName}
      />

      <!-- Community Description Textarea -->
      <LocalizedTextarea
        {locale}
        label={t.communityDescription}
        placeholder={t.communityDescriptionPlaceholder}
        rows={4}
        required
        bind:value={communityDescription}
      />
    </form>
  </Modal>
{/if}

<style>
  .community-image-container {
    height: 200px;
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
  }

  .community-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .community-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
  }

  .search-container {
    position: relative;
  }

  .search-container input {
    padding-right: 2.5rem;
  }

  @media (max-width: 768px) {
    .community-image-container {
      height: 150px;
    }

    .search-container input {
      min-width: 200px !important;
    }
  }

  @media (max-width: 576px) {
    .search-container input {
      min-width: 150px !important;
    }
  }

  .card {
    transition: transform 0.2s ease-in-out;
  }

  .card:hover {
    transform: translateY(-2px);
  }

  /* Mobile optimizations */
  @media (max-width: 767.98px) {
    .card-body {
      padding: 1rem !important;
    }

    .card-title {
      font-size: 1.1rem;
    }

    .community-image-container {
      height: 120px;
    }

    .btn {
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
    }

    /* Better spacing for mobile */
    .row.g-4 {
      margin-bottom: 1rem;
    }

    .card {
      margin-bottom: 1rem;
    }

    /* Disable hover effects on mobile */
    .card:hover {
      transform: none;
    }
  }

  /* Tablet optimizations */
  @media (min-width: 768px) and (max-width: 991.98px) {
    .card-body {
      padding: 1.5rem !important;
    }
  }
</style>
