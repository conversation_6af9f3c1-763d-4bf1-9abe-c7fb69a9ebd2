import { Reactor } from "@commune/api";
import { ForbiddenException, Injectable, Logger } from "@nestjs/common";
import {
    toPrismaLocalizations,
    toPrismaLocalizationsWhere,
    toPrismaPagination,
} from "src/utils";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { MinioService } from "src/minio/minio.service";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class ReactorHubService {
    private readonly logger = new Logger(ReactorHubService.name);

    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
    ) {}

    async getHubIds() {
        return this.prisma.reactorHub
            .findMany({
                where: {
                    deletedAt: null,
                },
                select: {
                    id: true,
                },
            })
            .then((hubs) => hubs.map((hub) => hub.id));
    }

    async getHubs(input: Reactor.GetHubsInput, user: CurrentUser | null) {
        const { ids, query } = input;

        const hubs = await this.prisma.reactorHub.findMany({
            ...toPrismaPagination(input.pagination),
            where: Object.assign(
                {},
                ids && { id: { in: ids } },
                !user?.isAdmin && { deletedAt: null },

                query && {
                    OR: [
                        {
                            id: query,
                        },
                        {
                            name: toPrismaLocalizationsWhere(query),
                        },
                        {
                            description: toPrismaLocalizationsWhere(query),
                        },
                    ],
                },
            ),
            select: {
                id: true,
                headUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
                name: true,
                description: true,
                image: true,
                isOfficial: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: user?.isAdmin,
            },
        });

        return hubs;
    }

    async createHub(input: Reactor.CreateHubInput, user: CurrentUser) {
        if (!user.isAdmin) {
            if (input.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_future_head"),
                );
            }
        }

        const hub = await this.prisma.reactorHub.create({
            data: {
                headUserId: input.headUserId ?? user.id,
                name: {
                    create: toPrismaLocalizations(input.name, "name"),
                },
                description: {
                    create: toPrismaLocalizations(
                        input.description,
                        "description",
                    ),
                },
            },
        });

        return hub;
    }

    async updateHub(input: Reactor.UpdateHubInput, user: CurrentUser) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }

            if (input.isOfficial !== undefined) {
                throw new ForbiddenException(...getError("must_be_admin"));
            }
        }

        const { name, description } = input;

        await this.prisma.reactorHub.update({
            where: { id: input.id },
            data: {
                name: name && {
                    deleteMany: {},
                    create: toPrismaLocalizations(name, "name"),
                },
                description: description && {
                    deleteMany: {},
                    create: toPrismaLocalizations(description, "description"),
                },
                isOfficial: input.isOfficial,
            },
        });
    }

    async updateHubImage(
        id: string,
        file: Express.Multer.File,
        user: CurrentUser,
    ) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        await this.prisma.$transaction(async (trx) => {
            const imageUrl = await this.minioService.uploadImage(
                file,
                "reactor-hub",
                id,
            );

            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });

            await trx.reactorHub.update({
                where: { id },
                data: {
                    imageId: image.id,
                },
            });
        });
    }

    async deleteHubImage(id: string, user: CurrentUser) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id, deletedAt: null },
            include: {
                image: true,
            },
        });

        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        const hubImage = hub.image;

        if (!hubImage) {
            return;
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.reactorHub.update({
                where: { id },
                data: {
                    imageId: null,
                },
            });

            await trx.image.update({
                where: { id: hubImage.id },
                data: {
                    deletedAt: new Date(),
                },
            });

            // await this.minioService.deleteReactorHubImage(id, hubImage.url);
        });
    }
}
